import sys, os
from pathlib import Path
from playwright.sync_api import sync_playwright

BASE_URL = os.environ.get("TARGET_URL", "https://openai-proxy.int.prod-southcentralus-hpe-3.dev.openai.org/")
USER_DATA_DIR = os.environ.get("USER_DATA_DIR", str(Path.cwd() / ".oai_auth_profile"))  # Persistent user directory
COOKIE_NAME = os.environ.get("COOKIE_NAME", "_oauth2_proxy")
COOKIE_DOMAIN_SUFFIX = os.environ.get("COOKIE_DOMAIN_SUFFIX", ".dev.openai.org")  # Your domain suffix

def pick_cookie(cookies):
    # Pick from all cookies: name matches and domain falls under .dev.openai.org (you can modify as needed)
    # Prioritize by latest expiration time/creation time
    candidates = [c for c in cookies if c.get("name") == COOKIE_NAME and c.get("domain","").endswith(COOKIE_DOMAIN_SUFFIX)]
    if not candidates:
        return None
    # Select the one with the largest expires value
    candidates.sort(key=lambda c: c.get("expires") or 0, reverse=True)
    return candidates[0]

def main():
    with sync_playwright() as p:
        context = p.chromium.launch_persistent_context(
            USER_DATA_DIR,
            headless=False,  # For first login, recommend --login (with head)
            args=[],
        )
        page = context.new_page()

        # Visit target domain, will automatically 302 redirect to login; first run you manually complete login and MFA
        page.goto(BASE_URL, wait_until="networkidle")

        # If in login mode, give enough time to complete MFA/consent etc. (can be adjusted as needed/continue on prompt)
        print(f"[info] Browser opened, please complete login. Press Enter after completion to continue capturing Cookie...", file=sys.stderr)
        try:
            input()
        except KeyboardInterrupt:
            context.close(); return

        # Refresh again to ensure proxy writes site cookies properly
        page.goto(BASE_URL, wait_until="networkidle")

        # Read cookies
        cookies = context.cookies()  # Cookies from all domains
        target = pick_cookie(cookies)
        context.close()

        if not target:
            print("[error] Target cookie not found (may not be logged in yet, cookie domain mismatch, or proxy hasn't set cookie).", file=sys.stderr)
            sys.exit(2)

        # Print only the value directly for easy script integration
        print(target.get("value"))

if __name__ == "__main__":
    main()
